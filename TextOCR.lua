-- TextOCR Addon for World of Warcraft
-- Displays whisper messages in OCR-optimized format

print("TextOCR: Lua file loading...")

local TextOCR = {}
TextOCR.frame = nil
TextOCR.hideTimer = nil
TextOCR.displayDuration = 10 -- seconds to display message

-- Event handler (определяем рано, чтобы XML мог его найти)
function TextOCR_OnEvent(self, event, ...)
    print("TextOCR: Event received - " .. tostring(event))

    if event == "ADDON_LOADED" then
        local addonName = ...
        if addonName == "TextOCR" then
            TextOCR_Initialize()
        end
    elseif event == "CHAT_MSG_WHISPER" then
        local message, sender = ...
        print("TextOCR: WHISPER received from " .. tostring(sender) .. ": " .. tostring(message))
        TextOCR_DisplayMessage(sender, message, "RECEIVED")
    elseif event == "CHAT_MSG_WHISPER_INFORM" then
        local message, sender = ...
        print("TextOCR: WHISPER_INFORM sent to " .. tostring(sender) .. ": " .. tostring(message))
        TextOCR_DisplayMessage(sender, message, "SENT")
    end
end

-- Initialize addon (определяем рано для XML)
function TextOCR_OnLoad(self)
    print("TextOCR: OnLoad called with frame: " .. tostring(self:GetName()))
    TextOCR.frame = self

    -- Register events for whisper messages
    print("TextOCR: Registering events...")
    self:RegisterEvent("CHAT_MSG_WHISPER")
    self:RegisterEvent("CHAT_MSG_WHISPER_INFORM")
    self:RegisterEvent("ADDON_LOADED")
    print("TextOCR: Events registered: CHAT_MSG_WHISPER, CHAT_MSG_WHISPER_INFORM, ADDON_LOADED")

    -- Set event handler
    self:SetScript("OnEvent", TextOCR_OnEvent)

    -- Make frame movable
    self:SetMovable(true)
    self:EnableMouse(true)
    self:RegisterForDrag("LeftButton")
    self:SetScript("OnDragStart", self.StartMoving)
    self:SetScript("OnDragStop", self.StopMovingOrSizing)

    print("TextOCR: Frame initialized successfully")
end

-- Frame show handler
function TextOCR_OnShow(self)
    -- Frame is now visible
end

-- Frame hide handler
function TextOCR_OnHide(self)
    -- Save position when hiding
    if TextOCRDB then
        local point, _, _, x, y = self:GetPoint()
        TextOCRDB.position = {
            point = point,
            x = x,
            y = y
        }
    end
end

-- Создаем глобальный обработчик событий как резервный вариант
local eventFrame = CreateFrame("Frame")
eventFrame:RegisterEvent("ADDON_LOADED")
eventFrame:RegisterEvent("CHAT_MSG_WHISPER")
eventFrame:RegisterEvent("CHAT_MSG_WHISPER_INFORM")

eventFrame:SetScript("OnEvent", function(self, event, ...)
    print("TextOCR: Backup event handler - " .. tostring(event))
    TextOCR_OnEvent(self, event, ...)
end)

print("TextOCR: Backup event handler created")

-- Инициализируем аддон сразу при загрузке файла
C_Timer.After(1, function()
    print("TextOCR: Auto-initializing addon...")
    if not TextOCRDB then
        TextOCR_Initialize()
    end
end)



-- Initialize addon settings
function TextOCR_Initialize()
    print("TextOCR: Initializing addon...")

    -- Initialize saved variables if needed
    if not TextOCRDB then
        TextOCRDB = {
            position = { point = "TOPLEFT", x = 50, y = -50 },
            enabled = true,
            displayDuration = 10,
            fixedPosition = true,  -- Always show in fixed position
            battlenetForward = true,  -- Forward to Battle.net (включено по умолчанию)
            battlenetTarget = nil  -- Battle.net friend ID to forward to
        }
        print("TextOCR: Created new settings database")
    else
        print("TextOCR: Loaded existing settings")
        -- Add new settings if they don't exist (for existing users)
        if TextOCRDB.fixedPosition == nil then
            TextOCRDB.fixedPosition = true
        end
        if TextOCRDB.battlenetForward == nil then
            TextOCRDB.battlenetForward = true  -- Включаем по умолчанию для существующих пользователей
        end
        if TextOCRDB.battlenetTarget == nil then
            TextOCRDB.battlenetTarget = nil
        end
    end

    -- Restore frame position
    if TextOCR.frame and TextOCRDB.position then
        TextOCR.frame:ClearAllPoints()
        TextOCR.frame:SetPoint(
            TextOCRDB.position.point or "CENTER",
            UIParent,
            TextOCRDB.position.point or "CENTER",
            TextOCRDB.position.x or 0,
            TextOCRDB.position.y or 200
        )
        print("TextOCR: Restored frame position")
    end

    TextOCR.displayDuration = TextOCRDB.displayDuration or 10
    print("TextOCR: Initialization complete")
end

-- Display whisper message in OCR-optimized format
function TextOCR_DisplayMessage(sender, message, messageType)
    print("TextOCR: DisplayMessage called - sender: " .. tostring(sender) .. ", message: " .. tostring(message) .. ", type: " .. tostring(messageType))

    -- Ensure TextOCRDB is initialized
    if not TextOCRDB then
        TextOCR_Initialize()
    end

    if not TextOCRDB.enabled then
        print("TextOCR: Addon disabled, skipping message")
        return
    end

    -- Пересылаем сообщение в Battle.net чат (если включено)
    if TextOCRDB.battlenetForward then
        print("TextOCR: Battle.net forwarding enabled, calling ForwardToBattlenet")
        TextOCR_ForwardToBattlenet(sender, message, messageType)
    else
        print("TextOCR: Battle.net forwarding disabled")
    end

    local frame = TextOCR_GetFrame()
    if not frame then
        print("TextOCR: Error - could not create frame!")
        return
    end
    
    -- Clear any existing hide timer
    if TextOCR.hideTimer then
        TextOCR.hideTimer:Cancel()
        TextOCR.hideTimer = nil
    end
    
    -- Format sender text with message type indicator
    local senderText = ""
    if messageType == "RECEIVED" then
        senderText = "FROM: " .. (sender or "Unknown")
    else
        senderText = "TO: " .. (sender or "Unknown")
    end
    
    -- Set text content - try both XML-based and manually created elements
    local senderFontString = _G[frame:GetName() .. "SenderText"] or frame.senderText
    local messageFontString = _G[frame:GetName() .. "MessageText"] or frame.messageText
    local timestampFontString = _G[frame:GetName() .. "TimestampText"] or frame.timestampText

    if senderFontString then
        senderFontString:SetText(senderText)
        -- Use large, bold font for better OCR - white text on black background
        senderFontString:SetFont("Fonts\\FRIZQT__.TTF", 20, "OUTLINE")
        senderFontString:SetTextColor(1, 1, 1, 1) -- Белый цвет
    else
        print("TextOCR: Warning - sender text element not found")
    end

    if messageFontString then
        messageFontString:SetText(message or "")
        -- Use large, bold font for better OCR - white text on black background
        messageFontString:SetFont("Fonts\\FRIZQT__.TTF", 18, "OUTLINE")
        messageFontString:SetTextColor(1, 1, 1, 1) -- Белый цвет
    else
        print("TextOCR: Warning - message text element not found")
    end

    if timestampFontString then
        timestampFontString:SetText(date("%H:%M:%S"))
        timestampFontString:SetFont("Fonts\\FRIZQT__.TTF", 14, "OUTLINE")
        timestampFontString:SetTextColor(0.8, 0.8, 0.8, 1) -- Светло-серый цвет
    else
        print("TextOCR: Warning - timestamp text element not found")
    end
    
    -- Set frame to fixed position if enabled
    if TextOCRDB.fixedPosition then
        frame:ClearAllPoints()
        frame:SetPoint(
            TextOCRDB.position.point or "TOPLEFT",
            UIParent,
            TextOCRDB.position.point or "TOPLEFT",
            TextOCRDB.position.x or 50,
            TextOCRDB.position.y or -50
        )
    end

    -- Show the frame
    frame:Show()

    -- Set timer to hide frame after specified duration
    TextOCR.hideTimer = C_Timer.NewTimer(TextOCR.displayDuration, function()
        frame:Hide()
        TextOCR.hideTimer = nil
    end)
    
    -- Debug output
    print("TextOCR: Displaying message from " .. (sender or "Unknown") .. ": " .. (message or ""))
end

-- Пересылка сообщения в Battle.net чат
function TextOCR_ForwardToBattlenet(sender, message, messageType)
    print("TextOCR: ForwardToBattlenet called - sender: " .. tostring(sender) .. ", message: " .. tostring(message) .. ", type: " .. tostring(messageType))
    print("TextOCR: battlenetForward: " .. tostring(TextOCRDB.battlenetForward) .. ", battlenetTarget: " .. tostring(TextOCRDB.battlenetTarget))

    -- Отправляем только если установлена конкретная цель
    if not TextOCRDB.battlenetTarget or TextOCRDB.battlenetTarget == "" then
        print("TextOCR: No Battle.net target set, skipping forward")
        return
    end

    -- Формируем сообщение для пересылки
    local prefix = messageType == "RECEIVED" and "FROM" or "TO"
    local forwardMessage = string.format("[%s: %s] %s", prefix, sender or "Unknown", message or "")

    -- Отправляем в Battle.net чат
    BNSendWhisper(TextOCRDB.battlenetTarget, forwardMessage)
    print("TextOCR: Forwarded to Battle.net - " .. forwardMessage)
end

-- Получение списка Battle.net друзей для настройки
function TextOCR_GetBattlenetFriends()
    local friends = {}
    local numBNetTotal, numBNetOnline = BNGetNumFriends()

    for i = 1, numBNetTotal do
        local accountInfo = C_BattleNet.GetFriendAccountInfo(i)
        if accountInfo and accountInfo.accountName then
            table.insert(friends, {
                id = accountInfo.bnetAccountID,
                name = accountInfo.accountName,
                battleTag = accountInfo.battleTag,
                online = accountInfo.isOnline
            })
        end
    end

    return friends
end



-- Slash commands
SLASH_TEXTOCR1 = "/textocr"
SLASH_TEXTOCR2 = "/tocr"

-- Helper function to get or create frame
function TextOCR_GetFrame()
    if not TextOCR.frame then
        -- Try to find the frame by name
        TextOCR.frame = _G["TextOCRFrame"]
        if TextOCR.frame then
            print("TextOCR: Found existing frame")
        else
            print("TextOCR: Frame not found, trying to create...")
            -- Try to create frame manually if XML didn't load
            TextOCR.frame = CreateFrame("Frame", "TextOCRFrame", UIParent)
            if TextOCR.frame then
                TextOCR_SetupFrame(TextOCR.frame)
                print("TextOCR: Created frame manually")
            end
        end
    end
    return TextOCR.frame
end

-- Setup frame manually if needed
function TextOCR_SetupFrame(frame)
    frame:SetSize(600, 200)
    frame:SetPoint("TOPLEFT", UIParent, "TOPLEFT", 50, -50)
    frame:SetMovable(true)
    frame:EnableMouse(true)
    frame:RegisterForDrag("LeftButton")
    frame:SetScript("OnDragStart", frame.StartMoving)
    frame:SetScript("OnDragStop", frame.StopMovingOrSizing)

    -- Create solid black background for OCR - multiple methods to ensure it works
    frame.bg = frame:CreateTexture(nil, "BACKGROUND")
    frame.bg:SetAllPoints(frame)
    frame.bg:SetColorTexture(0, 0, 0, 1) -- Solid black background
    frame.bg:SetTexture(nil) -- Clear any existing texture
    frame.bg:SetVertexColor(0, 0, 0, 1) -- Ensure black color

    -- Add a border for better visibility
    frame.border = frame:CreateTexture(nil, "BORDER")
    frame.border:SetPoint("TOPLEFT", frame, "TOPLEFT", -2, 2)
    frame.border:SetPoint("BOTTOMRIGHT", frame, "BOTTOMRIGHT", 2, -2)
    frame.border:SetColorTexture(0.2, 0.2, 0.2, 1) -- Dark gray border

    -- Create text elements with OCR-optimized settings
    frame.senderText = frame:CreateFontString(nil, "OVERLAY", "GameFontNormalLarge")
    frame.senderText:SetPoint("TOPLEFT", 15, -15)
    frame.senderText:SetSize(570, 35)
    frame.senderText:SetJustifyH("LEFT")
    frame.senderText:SetFont("Fonts\\FRIZQT__.TTF", 20, "OUTLINE")
    frame.senderText:SetTextColor(1, 1, 1, 1) -- White text

    frame.messageText = frame:CreateFontString(nil, "OVERLAY", "GameFontNormalLarge")
    frame.messageText:SetPoint("TOPLEFT", 15, -55)
    frame.messageText:SetSize(570, 110)
    frame.messageText:SetJustifyH("LEFT")
    frame.messageText:SetFont("Fonts\\FRIZQT__.TTF", 18, "OUTLINE")
    frame.messageText:SetTextColor(1, 1, 1, 1) -- White text

    frame.timestampText = frame:CreateFontString(nil, "OVERLAY", "GameFontNormal")
    frame.timestampText:SetPoint("BOTTOMRIGHT", -15, 15)
    frame.timestampText:SetSize(570, 25)
    frame.timestampText:SetJustifyH("RIGHT")
    frame.timestampText:SetFont("Fonts\\FRIZQT__.TTF", 14, "OUTLINE")
    frame.timestampText:SetTextColor(0.8, 0.8, 0.8, 1) -- Light gray text

    frame:Hide()
    print("TextOCR: Frame setup complete with black background")
end

function SlashCmdList.TEXTOCR(msg)
    local command = string.lower(msg or "")

    -- Ensure addon is initialized
    if not TextOCRDB then
        TextOCR_Initialize()
    end

    -- Ensure frame exists
    local frame = TextOCR_GetFrame()

    if command == "show" then
        print("TextOCR: Showing test message...")
        TextOCR_DisplayMessage("TestSender", "This is a test message for OCR", "RECEIVED")
    elseif command == "hide" then
        if frame then
            frame:Hide()
            print("TextOCR: Window hidden")
        else
            print("TextOCR: Error - could not create frame!")
        end
    elseif command == "toggle" then
        TextOCRDB.enabled = not TextOCRDB.enabled
        print("TextOCR: " .. (TextOCRDB.enabled and "Enabled" or "Disabled"))
    elseif command == "reset" then
        if frame then
            frame:ClearAllPoints()
            frame:SetPoint("TOPLEFT", UIParent, "TOPLEFT", 50, -50)
            TextOCRDB.position = { point = "TOPLEFT", x = 50, y = -50 }
            print("TextOCR: Position reset to top-left corner")
        else
            print("TextOCR: Error - could not create frame!")
        end
    elseif command == "fixed" then
        TextOCRDB.fixedPosition = not TextOCRDB.fixedPosition
        print("TextOCR: Fixed position " .. (TextOCRDB.fixedPosition and "enabled" or "disabled"))
        if TextOCRDB.fixedPosition then
            print("TextOCR: Messages will always appear at: " ..
                  TextOCRDB.position.point .. " " ..
                  TextOCRDB.position.x .. ", " ..
                  TextOCRDB.position.y)
        end
    elseif command == "battlenet" then
        TextOCRDB.battlenetForward = not TextOCRDB.battlenetForward
        print("TextOCR: Battle.net forwarding " .. (TextOCRDB.battlenetForward and "enabled" or "disabled"))
        if TextOCRDB.battlenetForward and not TextOCRDB.battlenetTarget then
            print("TextOCR: Use '/textocr friends' to see available Battle.net friends")
            print("TextOCR: Use '/textocr target <name>' to set forwarding target")
        end
    elseif command:match("^target%s+(.+)") then
        local targetName = command:match("^target%s+(.+)")
        local friends = TextOCR_GetBattlenetFriends()
        local found = false

        for _, friend in ipairs(friends) do
            if friend.name:lower():find(targetName:lower()) or
               (friend.battleTag and friend.battleTag:lower():find(targetName:lower())) then
                TextOCRDB.battlenetTarget = friend.id
                print("TextOCR: Battle.net target set to " .. friend.name .. " (" .. (friend.battleTag or "Unknown") .. ")")
                found = true
                break
            end
        end

        if not found then
            print("TextOCR: Friend not found. Use '/textocr friends' to see available friends")
        end
    elseif command == "friends" then
        local friends = TextOCR_GetBattlenetFriends()
        print("TextOCR: Available Battle.net friends:")
        for _, friend in ipairs(friends) do
            local status = friend.online and "|cff00ff00Online|r" or "|cffff0000Offline|r"
            print("- " .. friend.name .. " (" .. (friend.battleTag or "Unknown") .. ") " .. status)
        end
        if #friends == 0 then
            print("No Battle.net friends found")
        end
    elseif command == "debug" then
        print("TextOCR Debug Info:")
        print("- Frame exists: " .. tostring(TextOCR.frame ~= nil))
        print("- Global frame exists: " .. tostring(_G["TextOCRFrame"] ~= nil))
        print("- Event frame exists: " .. tostring(eventFrame ~= nil))
        print("- TextOCRDB exists: " .. tostring(TextOCRDB ~= nil))
        if TextOCRDB then
            print("- Enabled: " .. tostring(TextOCRDB.enabled))
            print("- Fixed position: " .. tostring(TextOCRDB.fixedPosition))
            print("- Battle.net forwarding: " .. tostring(TextOCRDB.battlenetForward))
            print("- Battle.net target: " .. tostring(TextOCRDB.battlenetTarget))
            if TextOCRDB.position then
                print("- Position: " .. TextOCRDB.position.point .. " " ..
                      TextOCRDB.position.x .. ", " .. TextOCRDB.position.y)
            end
        end
        if frame then
            print("- Frame visible: " .. tostring(frame:IsVisible()))
            print("- Frame name: " .. tostring(frame:GetName()))
        end
        -- Проверяем регистрацию событий
        if eventFrame then
            print("- Event frame registered for events")
        end
    elseif command == "test" then
        print("TextOCR: Testing event system...")
        -- Симулируем событие виспера
        TextOCR_OnEvent(eventFrame, "CHAT_MSG_WHISPER_INFORM", "test message", "TestPlayer")
    elseif command == "events" then
        print("TextOCR: Manually registering events...")
        if eventFrame then
            eventFrame:RegisterEvent("CHAT_MSG_WHISPER")
            eventFrame:RegisterEvent("CHAT_MSG_WHISPER_INFORM")
            print("TextOCR: Events re-registered")
        end
    else
        print("TextOCR Commands:")
        print("/textocr show - Show test message")
        print("/textocr hide - Hide display")
        print("/textocr toggle - Enable/disable addon")
        print("/textocr reset - Reset position to top-left")
        print("/textocr fixed - Toggle fixed position mode")
        print("/textocr battlenet - Toggle Battle.net forwarding")
        print("/textocr friends - List Battle.net friends")
        print("/textocr target <name> - Set Battle.net forwarding target")
        print("/textocr debug - Show debug info")
    end
end
