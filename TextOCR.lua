-- TextOCR Addon for World of Warcraft
-- Forwards received whisper messages to Battle.net with status display

print("TextOCR: Lua file loading...")

local TextOCR = {}
TextOCR.statusFrame = nil -- Постоянный статус фрейм

-- Event handler (определяем рано, чтобы XML мог его найти)
function TextOCR_OnEvent(self, event, ...)
    print("TextOCR: Event received - " .. tostring(event))

    if event == "ADDON_LOADED" then
        local addonName = ...
        if addonName == "TextOCR" then
            TextOCR_Initialize()
        end
    elseif event == "CHAT_MSG_WHISPER" then
        local message, sender = ...
        print("TextOCR: WHISPER received from " .. tostring(sender) .. ": " .. tostring(message))
        -- Пересылаем только полученные сообщения
        TextOCR_ForwardToBattlenet(sender, message)
    end
end

-- Initialize addon (определяем рано для XML)
function TextOCR_OnLoad(self)
    print("TextOCR: OnLoad called with frame: " .. tostring(self:GetName()))
    TextOCR.statusFrame = self

    -- Register events for whisper messages
    print("TextOCR: Registering events...")
    self:RegisterEvent("CHAT_MSG_WHISPER")
    self:RegisterEvent("ADDON_LOADED")
    print("TextOCR: Events registered: CHAT_MSG_WHISPER, ADDON_LOADED")

    -- Set event handler
    self:SetScript("OnEvent", TextOCR_OnEvent)

    -- Make frame movable
    self:SetMovable(true)
    self:EnableMouse(true)
    self:RegisterForDrag("LeftButton")
    self:SetScript("OnDragStart", self.StartMoving)
    self:SetScript("OnDragStop", self.StopMovingOrSizing)

    print("TextOCR: Status frame initialized successfully")
end

-- Frame show handler
function TextOCR_OnShow(self)
    -- Update status display when shown
    TextOCR_UpdateStatusDisplay()
end

-- Frame hide handler
function TextOCR_OnHide(self)
    -- Save position when hiding
    if TextOCRDB then
        local point, _, _, x, y = self:GetPoint()
        TextOCRDB.position = {
            point = point,
            x = x,
            y = y
        }
    end
end

-- Создаем глобальный обработчик событий как резервный вариант
local eventFrame = CreateFrame("Frame")
eventFrame:RegisterEvent("ADDON_LOADED")
eventFrame:RegisterEvent("CHAT_MSG_WHISPER")

eventFrame:SetScript("OnEvent", function(self, event, ...)
    print("TextOCR: Backup event handler - " .. tostring(event))
    TextOCR_OnEvent(self, event, ...)
end)

print("TextOCR: Backup event handler created")

-- Инициализируем аддон сразу при загрузке файла
C_Timer.After(1, function()
    print("TextOCR: Auto-initializing addon...")
    if not TextOCRDB then
        TextOCR_Initialize()
    end
end)



-- Initialize addon settings
function TextOCR_Initialize()
    print("TextOCR: Initializing addon...")

    -- Initialize saved variables if needed
    if not TextOCRDB then
        TextOCRDB = {
            position = { point = "TOPLEFT", x = 50, y = -50 },
            enabled = true,
            battlenetForward = true,  -- Forward to Battle.net (включено по умолчанию)
            battlenetTarget = nil  -- Battle.net friend ID to forward to
        }
        print("TextOCR: Created new settings database")
    else
        print("TextOCR: Loaded existing settings")
        -- Add new settings if they don't exist (for existing users)
        if TextOCRDB.battlenetForward == nil then
            TextOCRDB.battlenetForward = true  -- Включаем по умолчанию для существующих пользователей
        end
        if TextOCRDB.battlenetTarget == nil then
            TextOCRDB.battlenetTarget = nil
        end
    end

    -- Restore frame position
    if TextOCR.statusFrame and TextOCRDB.position then
        TextOCR.statusFrame:ClearAllPoints()
        TextOCR.statusFrame:SetPoint(
            TextOCRDB.position.point or "TOPLEFT",
            UIParent,
            TextOCRDB.position.point or "TOPLEFT",
            TextOCRDB.position.x or 50,
            TextOCRDB.position.y or -50
        )
        print("TextOCR: Restored frame position")
    end

    -- Создаем и показываем статус фрейм
    TextOCR_CreateStatusFrame()
    TextOCR_UpdateStatusDisplay()

    print("TextOCR: Initialization complete")
end

-- Создание постоянного статус фрейма
function TextOCR_CreateStatusFrame()
    if TextOCR.statusFrame then
        return -- Фрейм уже создан
    end

    -- Попробуем найти существующий фрейм из XML
    TextOCR.statusFrame = _G["TextOCRStatusFrame"]
    if not TextOCR.statusFrame then
        -- Создаем новый фрейм вручную, если XML не загрузился
        TextOCR.statusFrame = CreateFrame("Frame", "TextOCRStatusFrame", UIParent)
        TextOCR_SetupStatusFrame(TextOCR.statusFrame)
        print("TextOCR: Created status frame manually")
    else
        print("TextOCR: Found XML status frame")
    end

    -- Показываем фрейм постоянно
    TextOCR.statusFrame:Show()
end

-- Настройка статус фрейма
function TextOCR_SetupStatusFrame(frame)
    frame:SetSize(300, 80)
    frame:SetPoint("TOPLEFT", UIParent, "TOPLEFT", 50, -50)
    frame:SetMovable(true)
    frame:EnableMouse(true)
    frame:RegisterForDrag("LeftButton")
    frame:SetScript("OnDragStart", frame.StartMoving)
    frame:SetScript("OnDragStop", frame.StopMovingOrSizing)

    -- Создаем фон
    frame.bg = frame:CreateTexture(nil, "BACKGROUND")
    frame.bg:SetAllPoints(frame)
    frame.bg:SetColorTexture(0, 0, 0, 0.8) -- Полупрозрачный черный фон

    -- Добавляем рамку
    frame.border = frame:CreateTexture(nil, "BORDER")
    frame.border:SetPoint("TOPLEFT", frame, "TOPLEFT", -2, 2)
    frame.border:SetPoint("BOTTOMRIGHT", frame, "BOTTOMRIGHT", 2, -2)
    frame.border:SetColorTexture(0.3, 0.3, 0.3, 1) -- Серая рамка

    -- Создаем текстовые элементы
    frame.statusText = frame:CreateFontString(nil, "OVERLAY", "GameFontNormal")
    frame.statusText:SetPoint("TOPLEFT", 10, -10)
    frame.statusText:SetSize(280, 20)
    frame.statusText:SetJustifyH("LEFT")
    frame.statusText:SetFont("Fonts\\FRIZQT__.TTF", 12, "OUTLINE")
    frame.statusText:SetTextColor(1, 1, 1, 1) -- Белый текст

    frame.friendText = frame:CreateFontString(nil, "OVERLAY", "GameFontNormal")
    frame.friendText:SetPoint("TOPLEFT", 10, -30)
    frame.friendText:SetSize(280, 20)
    frame.friendText:SetJustifyH("LEFT")
    frame.friendText:SetFont("Fonts\\FRIZQT__.TTF", 12, "OUTLINE")
    frame.friendText:SetTextColor(1, 1, 1, 1) -- Белый текст

    frame.lastMessageText = frame:CreateFontString(nil, "OVERLAY", "GameFontNormal")
    frame.lastMessageText:SetPoint("TOPLEFT", 10, -50)
    frame.lastMessageText:SetSize(280, 20)
    frame.lastMessageText:SetJustifyH("LEFT")
    frame.lastMessageText:SetFont("Fonts\\FRIZQT__.TTF", 10, "OUTLINE")
    frame.lastMessageText:SetTextColor(0.8, 0.8, 0.8, 1) -- Светло-серый текст

    print("TextOCR: Status frame setup complete")
end

-- Обновление отображения статуса
function TextOCR_UpdateStatusDisplay()
    if not TextOCR.statusFrame then
        return
    end

    local statusText = ""
    local friendText = ""

    if TextOCRDB.battlenetForward then
        statusText = "|cff00ff00Battle.net: Включено|r"
        if TextOCRDB.battlenetTarget then
            local friendName = TextOCR_GetFriendNameById(TextOCRDB.battlenetTarget)
            if friendName then
                friendText = "Друг: " .. friendName
            else
                friendText = "|cffff0000Друг: Не найден|r"
            end
        else
            friendText = "|cffff0000Друг: Не выбран|r"
        end
    else
        statusText = "|cffff0000Battle.net: Отключено|r"
        friendText = "Друг: -"
    end

    -- Попробуем найти элементы как в XML, так и созданные вручную
    local statusTextElement = _G[TextOCR.statusFrame:GetName() .. "StatusText"] or TextOCR.statusFrame.statusText
    local friendTextElement = _G[TextOCR.statusFrame:GetName() .. "FriendText"] or TextOCR.statusFrame.friendText
    local lastMessageTextElement = _G[TextOCR.statusFrame:GetName() .. "LastMessageText"] or TextOCR.statusFrame.lastMessageText

    if statusTextElement then
        statusTextElement:SetText(statusText)
    end
    if friendTextElement then
        friendTextElement:SetText(friendText)
    end
    if lastMessageTextElement then
        lastMessageTextElement:SetText("Последнее: " .. (TextOCRDB.lastForwardTime or "Нет"))
    end
end

-- Пересылка сообщения в Battle.net чат (только полученные сообщения)
function TextOCR_ForwardToBattlenet(sender, message)
    print("TextOCR: ForwardToBattlenet called - sender: " .. tostring(sender) .. ", message: " .. tostring(message))

    -- Проверяем, включена ли пересылка
    if not TextOCRDB.battlenetForward then
        print("TextOCR: Battle.net forwarding disabled")
        return
    end

    -- Отправляем только если установлена конкретная цель
    if not TextOCRDB.battlenetTarget or TextOCRDB.battlenetTarget == "" then
        print("TextOCR: No Battle.net target set, skipping forward")
        return
    end

    -- Формируем сообщение для пересылки с таймштампом
    local timestamp = date("%H:%M:%S")
    local forwardMessage = string.format("[%s] FROM %s: %s", timestamp, sender or "Unknown", message or "")

    -- Отправляем в Battle.net чат
    BNSendWhisper(TextOCRDB.battlenetTarget, forwardMessage)

    -- Сохраняем время последней отправки
    TextOCRDB.lastForwardTime = timestamp

    -- Обновляем статус
    TextOCR_UpdateStatusDisplay()

    print("TextOCR: Forwarded to Battle.net - " .. forwardMessage)
end

-- Получение имени друга по ID
function TextOCR_GetFriendNameById(friendId)
    local numBNetTotal, numBNetOnline = BNGetNumFriends()

    for i = 1, numBNetTotal do
        local accountInfo = C_BattleNet.GetFriendAccountInfo(i)
        if accountInfo and accountInfo.bnetAccountID == friendId then
            return accountInfo.accountName or accountInfo.battleTag
        end
    end

    return nil
end

-- Получение списка Battle.net друзей для настройки
function TextOCR_GetBattlenetFriends()
    local friends = {}
    local numBNetTotal, numBNetOnline = BNGetNumFriends()

    for i = 1, numBNetTotal do
        local accountInfo = C_BattleNet.GetFriendAccountInfo(i)
        if accountInfo and accountInfo.accountName then
            table.insert(friends, {
                id = accountInfo.bnetAccountID,
                name = accountInfo.accountName,
                battleTag = accountInfo.battleTag,
                online = accountInfo.isOnline
            })
        end
    end

    return friends
end



-- Slash commands
SLASH_TEXTOCR1 = "/textocr"
SLASH_TEXTOCR2 = "/tocr"

-- Helper function to get or create status frame
function TextOCR_GetStatusFrame()
    if not TextOCR.statusFrame then
        TextOCR_CreateStatusFrame()
    end
    return TextOCR.statusFrame
end

function SlashCmdList.TEXTOCR(msg)
    local command = string.lower(msg or "")

    -- Ensure addon is initialized
    if not TextOCRDB then
        TextOCR_Initialize()
    end

    -- Ensure status frame exists
    local frame = TextOCR_GetStatusFrame()

    if command == "show" then
        if frame then
            frame:Show()
            TextOCR_UpdateStatusDisplay()
            print("TextOCR: Status window shown")
        end
    elseif command == "hide" then
        if frame then
            frame:Hide()
            print("TextOCR: Status window hidden")
        end
    elseif command == "toggle" then
        TextOCRDB.enabled = not TextOCRDB.enabled
        TextOCR_UpdateStatusDisplay()
        print("TextOCR: " .. (TextOCRDB.enabled and "Enabled" or "Disabled"))
    elseif command == "reset" then
        if frame then
            frame:ClearAllPoints()
            frame:SetPoint("TOPLEFT", UIParent, "TOPLEFT", 50, -50)
            TextOCRDB.position = { point = "TOPLEFT", x = 50, y = -50 }
            print("TextOCR: Position reset to top-left corner")
        end
    elseif command == "battlenet" then
        TextOCRDB.battlenetForward = not TextOCRDB.battlenetForward
        TextOCR_UpdateStatusDisplay()
        print("TextOCR: Battle.net forwarding " .. (TextOCRDB.battlenetForward and "enabled" or "disabled"))
        if TextOCRDB.battlenetForward and not TextOCRDB.battlenetTarget then
            print("TextOCR: Use '/textocr friends' to see available Battle.net friends")
            print("TextOCR: Use '/textocr target <name>' to set forwarding target")
        end
    elseif command:match("^target%s+(.+)") then
        local targetName = command:match("^target%s+(.+)")
        local friends = TextOCR_GetBattlenetFriends()
        local found = false

        for _, friend in ipairs(friends) do
            if friend.name:lower():find(targetName:lower()) or
               (friend.battleTag and friend.battleTag:lower():find(targetName:lower())) then
                TextOCRDB.battlenetTarget = friend.id
                TextOCR_UpdateStatusDisplay()
                print("TextOCR: Battle.net target set to " .. friend.name .. " (" .. (friend.battleTag or "Unknown") .. ")")
                found = true
                break
            end
        end

        if not found then
            print("TextOCR: Friend not found. Use '/textocr friends' to see available friends")
        end
    elseif command == "friends" then
        local friends = TextOCR_GetBattlenetFriends()
        print("TextOCR: Available Battle.net friends:")
        for _, friend in ipairs(friends) do
            local status = friend.online and "|cff00ff00Online|r" or "|cffff0000Offline|r"
            print("- " .. friend.name .. " (" .. (friend.battleTag or "Unknown") .. ") " .. status)
        end
        if #friends == 0 then
            print("No Battle.net friends found")
        end
    elseif command == "debug" then
        print("TextOCR Debug Info:")
        print("- Status frame exists: " .. tostring(TextOCR.statusFrame ~= nil))
        print("- Event frame exists: " .. tostring(eventFrame ~= nil))
        print("- TextOCRDB exists: " .. tostring(TextOCRDB ~= nil))
        if TextOCRDB then
            print("- Enabled: " .. tostring(TextOCRDB.enabled))
            print("- Battle.net forwarding: " .. tostring(TextOCRDB.battlenetForward))
            print("- Battle.net target: " .. tostring(TextOCRDB.battlenetTarget))
            print("- Last forward time: " .. tostring(TextOCRDB.lastForwardTime))
            if TextOCRDB.position then
                print("- Position: " .. TextOCRDB.position.point .. " " ..
                      TextOCRDB.position.x .. ", " .. TextOCRDB.position.y)
            end
        end
        if frame then
            print("- Status frame visible: " .. tostring(frame:IsVisible()))
            print("- Status frame name: " .. tostring(frame:GetName()))
        end
        -- Проверяем регистрацию событий
        if eventFrame then
            print("- Event frame registered for events")
        end
    elseif command == "test" then
        print("TextOCR: Testing whisper forwarding...")
        -- Симулируем событие виспера
        TextOCR_ForwardToBattlenet("TestPlayer", "This is a test message")
    elseif command == "events" then
        print("TextOCR: Manually registering events...")
        if eventFrame then
            eventFrame:RegisterEvent("CHAT_MSG_WHISPER")
            print("TextOCR: Events re-registered")
        end
    else
        print("TextOCR Commands:")
        print("/textocr show - Show status window")
        print("/textocr hide - Hide status window")
        print("/textocr toggle - Enable/disable addon")
        print("/textocr reset - Reset position to top-left")
        print("/textocr battlenet - Toggle Battle.net forwarding")
        print("/textocr friends - List Battle.net friends")
        print("/textocr target <name> - Set Battle.net forwarding target")
        print("/textocr target <name> - Set Battle.net forwarding target")
        print("/textocr test - Test whisper forwarding")
        print("/textocr debug - Show debug info")
    end
end
