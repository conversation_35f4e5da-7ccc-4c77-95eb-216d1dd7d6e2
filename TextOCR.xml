<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/ ..\FrameXML\UI.xsd">
    
    <!-- Main OCR Display Frame -->
    <Frame name="TextOCRFrame" parent="UIParent" movable="true" enableMouse="true" clampedToScreen="true" hidden="true">
        <Size x="600" y="200"/>
        <Anchors>
            <Anchor point="TOPLEFT" x="50" y="-50"/>
        </Anchors>
        
        <!-- Solid Black Background for OCR -->
        <Layers>
            <Layer level="BACKGROUND">
                <Texture name="$parentBackground">
                    <Anchors>
                        <Anchor point="TOPLEFT"/>
                        <Anchor point="BOTTOMRIGHT"/>
                    </Anchors>
                    <Color r="0" g="0" b="0" a="1"/>
                </Texture>
            </Layer>
        </Layers>
        
        <!-- Title Bar -->
        <Frames>
            <Frame name="$parentTitleBar" inherits="TitleDragAreaTemplate">
                <Size x="0" y="25"/>
                <Anchors>
                    <Anchor point="TOPLEFT"/>
                    <Anchor point="TOPRIGHT"/>
                </Anchors>
            </Frame>
        </Frames>
        
        <!-- Close Button -->
        <Frames>
            <Button name="$parentCloseButton" inherits="UIPanelCloseButton">
                <Anchors>
                    <Anchor point="TOPRIGHT" x="-5" y="-5"/>
                </Anchors>
            </Button>
        </Frames>
        
        <!-- Sender Text -->
        <Layers>
            <Layer level="OVERLAY">
                <FontString name="$parentSenderText" inherits="GameFontNormalLarge" justifyH="LEFT">
                    <Size x="580" y="35"/>
                    <Anchors>
                        <Anchor point="TOPLEFT" x="15" y="-40"/>
                    </Anchors>
                    <Color r="1" g="1" b="1" a="1"/>
                    <FontHeight val="20"/>
                </FontString>
            </Layer>
        </Layers>

        <!-- Message Text -->
        <Layers>
            <Layer level="OVERLAY">
                <FontString name="$parentMessageText" inherits="GameFontNormalLarge" justifyH="LEFT">
                    <Size x="580" y="120"/>
                    <Anchors>
                        <Anchor point="TOPLEFT" x="15" y="-80"/>
                    </Anchors>
                    <Color r="1" g="1" b="1" a="1"/>
                    <FontHeight val="18"/>
                </FontString>
            </Layer>
        </Layers>

        <!-- Timestamp Text -->
        <Layers>
            <Layer level="OVERLAY">
                <FontString name="$parentTimestampText" inherits="GameFontNormal" justifyH="RIGHT">
                    <Size x="580" y="25"/>
                    <Anchors>
                        <Anchor point="BOTTOMRIGHT" x="-15" y="15"/>
                    </Anchors>
                    <Color r="0.8" g="0.8" b="0.8" a="1"/>
                    <FontHeight val="14"/>
                </FontString>
            </Layer>
        </Layers>
        
        <Scripts>
            <OnLoad>
                TextOCR_OnLoad(self)
            </OnLoad>
            <OnShow>
                TextOCR_OnShow(self)
            </OnShow>
            <OnHide>
                TextOCR_OnHide(self)
            </OnHide>
        </Scripts>
    </Frame>
    
</Ui>
