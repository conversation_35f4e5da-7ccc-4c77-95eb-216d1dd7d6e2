<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/ ..\FrameXML\UI.xsd">
    
    <!-- Main Status Display Frame -->
    <Frame name="TextOCRStatusFrame" parent="UIParent" movable="true" enableMouse="true" clampedToScreen="true" hidden="false">
        <Size x="300" y="80"/>
        <Anchors>
            <Anchor point="TOPLEFT" x="50" y="-50"/>
        </Anchors>

        <!-- Semi-transparent Background -->
        <Layers>
            <Layer level="BACKGROUND">
                <Texture name="$parentBackground">
                    <Anchors>
                        <Anchor point="TOPLEFT"/>
                        <Anchor point="BOTTOMRIGHT"/>
                    </Anchors>
                    <Color r="0" g="0" b="0" a="0.8"/>
                </Texture>
            </Layer>
        </Layers>
        
        <!-- Status Text -->
        <Layers>
            <Layer level="OVERLAY">
                <FontString name="$parentStatusText" inherits="GameFontNormal" justifyH="LEFT">
                    <Size x="280" y="20"/>
                    <Anchors>
                        <Anchor point="TOPLEFT" x="10" y="-10"/>
                    </Anchors>
                    <Color r="1" g="1" b="1" a="1"/>
                    <FontHeight val="12"/>
                </FontString>
            </Layer>
        </Layers>

        <!-- Friend Text -->
        <Layers>
            <Layer level="OVERLAY">
                <FontString name="$parentFriendText" inherits="GameFontNormal" justifyH="LEFT">
                    <Size x="280" y="20"/>
                    <Anchors>
                        <Anchor point="TOPLEFT" x="10" y="-30"/>
                    </Anchors>
                    <Color r="1" g="1" b="1" a="1"/>
                    <FontHeight val="12"/>
                </FontString>
            </Layer>
        </Layers>

        <!-- Last Message Text -->
        <Layers>
            <Layer level="OVERLAY">
                <FontString name="$parentLastMessageText" inherits="GameFontNormal" justifyH="LEFT">
                    <Size x="280" y="20"/>
                    <Anchors>
                        <Anchor point="TOPLEFT" x="10" y="-50"/>
                    </Anchors>
                    <Color r="0.8" g="0.8" b="0.8" a="1"/>
                    <FontHeight val="10"/>
                </FontString>
            </Layer>
        </Layers>
        
        <Scripts>
            <OnLoad>
                TextOCR_OnLoad(self)
            </OnLoad>
            <OnShow>
                TextOCR_OnShow(self)
            </OnShow>
            <OnHide>
                TextOCR_OnHide(self)
            </OnHide>
        </Scripts>
    </Frame>
    
</Ui>
